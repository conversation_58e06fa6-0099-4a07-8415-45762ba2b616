"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Upload, FileText, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useNotifications } from "@/contexts/notification-context";

export default function NotificationUploadPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const { addNotification } = useNotifications();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        return;
      }
      setSelectedFile(file);
      setUploadResult(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error("Please select a PDF file first");
      return;
    }

    setIsUploading(true);
    try {
      // Simulate upload process
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Create notification with sample PDF data
      addNotification({
        title: "PDF Upload Successful",
        message: `Your PDF file "${selectedFile.name}" has been uploaded successfully and is ready for processing.`,
        type: "success",
        pdfFileName: selectedFile.name,
        pdfUrl: URL.createObjectURL(selectedFile), // In a real app, this would be the server URL
        pdfData: {
          templateId: "good-moral-certificate",
          templateName: "Good Moral Certificate",
          placeholders: [
            "[AGE]",
            "[BARANGAY]",
            "[CTC NUMBER]",
            "[DAY]",
            "[FIRST NAME]",
            "[LAST NAME]",
            "[MIDDLE INITIAL]",
            "[MONTH]",
            "[O.R. NUMBER]",
            "[SUFFIX]",
            "[TIN NUMBER]",
            "[YEAR]",
          ],
          userData: {
            AGE: "21",
            BARANGAY: "San Roque",
            "CTC NUMBER": "1344434",
            DAY: "Empty",
            "FIRST NAME": "Brent Jeremiah",
            "LAST NAME": "Ortega",
            "MIDDLE INITIAL": "L",
            MONTH: "Empty",
            "O.R. NUMBER": "1344434",
            SUFFIX: "Jr.",
            "TIN NUMBER": "4324350",
            YEAR: "Empty",
          },
          photoBase64:
            "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=", // Sample base64 image
          generatedAt: new Date().toISOString(),
          layoutSize: "A4" as const,
        },
      });

      setUploadResult({
        success: true,
        message: "PDF uploaded successfully! Check your notifications.",
      });

      toast.success("PDF uploaded successfully!");

      // Reset form
      setSelectedFile(null);
      const fileInput = document.getElementById("pdf-file") as HTMLInputElement;
      if (fileInput) {
        fileInput.value = "";
      }
    } catch (error) {
      console.error("Error uploading PDF:", error);

      addNotification({
        title: "PDF Upload Failed",
        message: `Failed to upload "${selectedFile.name}". Please try again.`,
        type: "error",
        pdfFileName: selectedFile.name,
      });

      setUploadResult({
        success: false,
        message: "Failed to upload PDF. Please try again.",
      });

      toast.error("Failed to upload PDF");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">
          Upload PDF for Notifications
        </h1>
        <p className="text-muted-foreground">
          Upload a PDF file to trigger a notification. This demonstrates the
          notification system functionality.
        </p>
      </div>

      {/* File Upload Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload PDF File
          </CardTitle>
          <CardDescription>
            Select a PDF file to upload. A notification will be created once the
            upload is complete.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="pdf-file">PDF File</Label>
            <Input
              id="pdf-file"
              type="file"
              accept=".pdf"
              onChange={handleFileSelect}
              className="mt-1"
              disabled={isUploading}
            />
          </div>

          {selectedFile && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
              <FileText className="h-4 w-4 text-blue-500" />
              <div className="flex-1">
                <p className="text-sm font-medium">{selectedFile.name}</p>
                <p className="text-xs text-muted-foreground">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
          )}

          <Button
            onClick={handleUpload}
            disabled={!selectedFile || isUploading}
            className="w-full"
          >
            {isUploading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload PDF
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Upload Result */}
      {uploadResult && (
        <Card
          className={
            uploadResult.success
              ? "border-green-200 bg-green-50"
              : "border-red-200 bg-red-50"
          }
        >
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              {uploadResult.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
              <p
                className={`text-sm font-medium ${
                  uploadResult.success ? "text-green-800" : "text-red-800"
                }`}
              >
                {uploadResult.message}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">How it works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>1. Select a PDF file using the file input above</p>
          <p>2. Click "Upload PDF" to start the upload process</p>
          <p>
            3. A notification will appear in the notification dropdown (bell
            icon in the header)
          </p>
          <p>4. Check the notification dropdown to see your upload status</p>
        </CardContent>
      </Card>
    </div>
  );
}
